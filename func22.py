
import io

from tkcalendar import DateEntry
import socket
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *

from os import path
import requests,json, time
from datetime import datetime, timedelta, date
import pandas
from multiprocessing.dummy import Pool
import threading

from bs4 import BeautifulSoup

from tool import Tool
import datetime

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称
def getmailtrace(parent,mailno):
    global L
    print ('正在处理:'+mailno)
    dataOutput = {
        "总包号": [],
        "邮件号": []
    }
    # ffsj = getmailtime(mailno)
    # # 将日期字符串转换为datetime对象，方便进行格式化处理
    # if ffsj:
    #     date_time = datetime.datetime.strptime(ffsj, "%Y-%m-%d %H:%M:%S")
    #
    #     # 使用strftime函数将datetime对象格式化为"%Y-%m-%d"格式的字符串
    #     date_part = date_time.strftime("%Y-%m-%d")
    # else:
    #     date_part = (datetime.date.today()).strftime("%Y-%m-%d")
    # if ffsj is None:
    #     dataOutput["总包号"].append(mailno)
    #     dataOutput["邮件号"].append("")

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpspacket/qpspacketinternals?containerNo='+mailno+'&dispatchDate=2024-08-14%2019:38:22&traceType=4&___t0.6561345708859714'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpspacket/list',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    # data = {

    # 'contain_no': mailno

    # }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.get(url, headers=headers, verify=False)
    r = response.text
    html = BeautifulSoup(r, 'lxml')
    item = html.find_all('a')
    print(r)

    # indexNUm = 1

    # 通过循环来获取JSON中的数据，并添加到字典中
    for i in item:
        # print(i.string)
        dataOutput["总包号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内

        dataOutput["邮件号"].append(i.string)

    L += 1
    if L % 1000 == 0:
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))  # 立即调用UI更新方法

    return dataOutput
#
# def getmailtime(mailno):
#     global L
#     print('正在处理:' + mailno)
#
#     requests.packages.urllib3.disable_warnings()
#     url = 'https://**********/querypush-web/a/qps/qpspacket/queryCurrentPacketByContainNos'
#     headers = {
#         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
#         'Accept-Encoding': 'gzip, deflate, br',
#         'Accept-Language': 'zh-CN,zh;q=0.9',
#
#         'Connection': 'keep-alive',
#         'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
#         'Host': '**********',
#         'Referer': 'https://**********/querypush-web/a/qps/qpspacket/list',
#         'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
#         'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
#     }
#     data = {
#
#         'contain_nos': mailno,
#
#         'containOrVessel': 1
#
#     }
#     # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
#     response = session.post(url, headers=headers, params=data, verify=False)
#     r = response.text
#     try:
#         jsonObj = json.loads(r)
#     except json.decoder.JSONDecodeError:
#         print(mailno+"JSON解析失败")
#         return ""
#     if jsonObj:
#         if "dispatchDate" in jsonObj[-1] and jsonObj[-1]["dispatchDate"] is not None:
#             # indexNUm = 1
#             print(mailno+str(jsonObj[-1]["dispatchDate"]))
#             return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(jsonObj[-1]["dispatchDate"]/ 1000))
#         else:
#             return ""


def getgjmailtrace(parent,mailno):
    global L
    dataOutput = {
                "总包号": [],
                "邮件号": []
            }
    print('正在处理:' + mailno)
    today = (date.today() + timedelta(days=-1)).strftime(
        "%Y-%m-%d") + ' 23:59:59'  # + datetime.timedelta(days = -10)

    requests.packages.urllib3.disable_warnings()

    startday = (date.today() + timedelta(days=-260)).strftime("%Y-%m-%d") + ' 00:00:00'  # +
    print(startday)
    print(today)
    #url = 'https://**********/report-web/a/report/dispatchhistoryquerysum1/result'
    if selected.get() == '封发':
        url = 'https://**********/report-web/a/report/dispatchhistoryquerysum1/result'
    else:
        url = 'https://**********/report-web/a/report/openhistoryquerydetail/result'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '336',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/report-web/a/report/dispatchhistoryquerysum1/list',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }
    data = {

        'orgCode': '',#51040034
        'findSect': 'B',
        'parcelBarcode': '',
        'parcelNo': '',
        'bagBarcode': mailno,
        'bagNo': '',
        'itemId': '',
        'orgOriginCode': '',
        'orgOriginName': '',
        'orgDestCode': '',
        'orgDestName': '',
        'teamCode': '',
        'workerCode': '',
        'workSect': '',
        'flag': ' ',
        'transType': ' ',
        'impExp': ' ',
        'regInsFlag': '',
        'gridFlag': '',
        'flightNumber': '',
        'vipFlag': '',
        'startDate': startday,
        'endDate': today

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    #print(r)
    # html=BeautifulSoup(r,'html.parser')
    html = BeautifulSoup(r, 'lxml')
    jdj = html.find('tr', {'class': 'tab_body'})
    # print(jdj)
    if jdj:
        url = 'https://**********' + jdj.find('a')['href']

        tds = jdj.find_all('td')
        if len(tds) >= 13:
            # jdj = tds[2].get_text()
            # zl = tds[6].get_text()
            # ffsj = tds[11].get_text()
            # hbh = tds[12].get_text()

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Content-Length': '336',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'DNT': '1',
                'Host': '**********',
                'Origin': 'https://**********',
                'Referer': 'https://**********/report-web/a/report/dispatchhistoryquerysum1/result',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
                'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
            }
            response = session.get(url, headers=headers, data=data, verify=False)
            r = response.text
            html = BeautifulSoup(r, 'lxml')
            # print(html)
            if selected.get() == '封发':

                yj = html.find_all('input', {'type': 'checkbox'})
                checkbox_count = len(yj)
                print(f"共: {checkbox_count}件")
                if yj:

                    for i in yj:
                        # yjh=re.findall(r'itemid="(.+?)"', str(i))[0]
                        yjh = i.get('itemid')
                        dataOutput["总包号"].append(mailno)

                        dataOutput["邮件号"].append(yjh)
                L += 1
                if L % 1000 == 0:
                    parent.after(0, tool.process_input('已爬' + str(L) + '件'))  # 立即调用UI更新方法
            else:
                tr=html.find_all('tr', {'class': 'tab_body'})
                checkbox_count = len(tr)
                print(f"共: {checkbox_count}件")
                if tr:
                    for i in tr:
                        td = i.find_all('td')
                        yjh = td[5].get_text()
                        dataOutput["总包号"].append(mailno)
                        dataOutput["邮件号"].append(yjh)
                L += 1
                if L % 1000 == 0:
                    parent.after(0, tool.process_input('已爬' + str(L) + '件'))  # 立即调用UI更新方法
    else:
        dataOutput["总包号"].append(mailno)
        dataOutput["邮件号"].append('')
    return dataOutput

def getgnmailtrace(parent,mailbagNo):
    global L
    dataOutput = {
                "总包号": [],
                "邮件号": []
            }
    print('正在处理:' + mailbagNo)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/pcs-tc-web/a/pcs/parcelQuery/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '336',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/parcelQuery/toList',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }
    data = {

        'mailbagNumber': '',
        'mailbagNo': mailbagNo,
        'mailbagClassCode': '',
        'originOrgName': '',
        'originOrgCode': '',
        'destinationOrgName': '',
        'destinationOrgCode': ''

    }

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    #print(r)
    # html=BeautifulSoup(r,'html.parser')
    html = BeautifulSoup(r, 'lxml')
    # 设置基础 URL
    base_url = "https://**********"  # 请替换为实际网址


    # 解析初始页面，找到 a 标签中的参数
    a_tag = html.find("a", onclick=True)
    if a_tag:
        onclick_content = a_tag["onclick"]
        params = onclick_content.split("(")[-1].split(")")[0].replace("'", "").split(",")

        # 构造跳转 URL
        # jump_url = f"{base_url}/pcs-tc-web/a/pcs/parcelQuery/packList?id={params[0]}&opOrgCode={params[1]}&mailbagNumber={params[2]}&mailbagNo={params[3]}&originOrgCode={params[4]}"
        # 发送跳转请求
        url = "https://**********/pcs-tc-web/a/pcs/parcelQuery/packList"
        data = {
            "pageNo": 1,
            "pageSize": 200,
            "opOrgCode": params[1],
            "mailbagNo": params[2],
            "id": params[0]
        }
        response = session.post(url, data=data, verify=False)
        if response.status_code == 200:
            jump_html = BeautifulSoup(response.text, 'lxml')

            # 提取邮件号，每个 <tr> 的第一个 <td>
            for tr in jump_html.find_all("tr"):
                tds = tr.find_all("td")
                if tds:
                    mailno = tds[0].text.strip()
                    #if mailno.isdigit():  # 确保是邮件号
                    dataOutput["总包号"].append(mailbagNo)  # 假设 mailbagNumber 作为总包号
                    dataOutput["邮件号"].append(mailno)
            L += 1
            if L % 1000 == 0:
                parent.after(0, tool.process_input('已爬' + str(L) + '件'))  # 立即调用UI更新方法
    else:
        dataOutput["总包号"].append(mailbagNo)
        dataOutput["邮件号"].append('')
    return dataOutput


def getrealtime_mailtrace(parent, mailno):
    global L
    dataOutput = {
        "总包号": [],
        "邮件号": []
    }
    print('正在处理实时:' + mailno)

    requests.packages.urllib3.disable_warnings()

    url = 'https://**********/intproc-web/a/intproc/packPrint/search'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/packPrint/list',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    # 获取当前日期和十天前的日期
    # today = date.today()
    # ten_days_ago = today - timedelta(days=10)
    # # 格式化为字符串
    # shiftStartTimeSt = ten_days_ago.strftime("%Y-%m-%d") + ' 00:00:00'
    # shiftStartTimeEd = today.strftime("%Y-%m-%d") + ' 00:00:00'

    shiftStartTimeSt = start_date_entry.get_date().strftime('%Y-%m-%d') + ' 00:00:00'
    shiftStartTimeEd = end_date_entry.get_date().strftime('%Y-%m-%d') + ' 23:59:59'


    params = {
        'pageNo': '1',
        'pageSize': '20',
        'statistich': '',
        'orgCode': '',#51040034
        'searchType': '1',
        'parcelNo': '',
        'bagBarcode': mailno,
        'bagNo': '',
        'itemId': '',
        'orgOriginCode': '',
        'orgDestCode': '',
        'impExp': '',
        'teamCode': '',
        'workShiftId': '',
        'workerCode': '',
        'workSect': '',
        'flag': '',
        'ifVip': '',
        'transType': '',
        'regInsFlag': '',
        'gridFlag': '',
        'flightNumber': '',
        'shiftStartTimeSt': shiftStartTimeSt,
        'shiftStartTimeEd': shiftStartTimeEd,
        'dispStartTime': '',
        'dispEndTime': ''
    }

    response = session.get(url, headers=headers, params=params, verify=False)
    r = response.text
    #print(r)
    html = BeautifulSoup(r, 'lxml')

    # 提取明细链接参数
    detail_link = html.find('a', onclick=lambda x: x and 'showMores' in x)
    if detail_link:
        onclick_value = detail_link.get('onclick')
        params = onclick_value.replace('showMores(', '').replace(')', '').split(',')
        id_param = params[0].strip("'\" ")
        time_param = params[1].strip("'\" ")
        dom_bag_barcode = params[2].strip("'\" ")
        oe_bag_barcode = params[3].strip("'\" ")

        # 构造跳转URL
        jump_url = f'https://**********/intproc-web/a/intproc/packPrint/bagList?parcelId={id_param}&shiftStartTime={time_param}&flag={1 if dom_bag_barcode else 0}&pageNo=1&pageSize=2000'
        response = session.get(jump_url, headers=headers, verify=False)
        r = response.text
        html = BeautifulSoup(r, 'lxml')

        # 提取邮件号
        rows = html.select('#aabb tr')
        for row in rows:
            tds = row.find_all('td')
            if len(tds) > 1:
                mail_number = tds[1].get_text(strip=True)
                dataOutput["总包号"].append(mailno)
                dataOutput["邮件号"].append(mail_number)

    L += 1
    if L % 1000 == 0:
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))  # 立即调用UI更新方法

    return dataOutput

 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1



def run(title, parent):
    try:
        global username, password, session, jdptid, L,merged_data



        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        # 提交使用记录
        #tool.postlog(username, title, ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines


        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')
        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        name=None
        #url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        #url= 'https://**********/querypush-web/a/qps/qpspacket/list'
        #url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList'
        if organization_combobox.get()=='国际':
            if selected.get()=='封发':
                url = 'https://**********/report-web/a/report/dispatchhistoryquerysum1/list'
            elif selected.get()=='开拆':
                url = 'https://**********/report-web/a/report/openhistoryquerydetail/list'
            else:
                url = 'https://**********/intproc-web/a/intproc/packPrint/search'
        elif organization_combobox.get()=='国内':
            url = 'https://**********/pcs-tc-web/a/pcs/parcelQuery/toList'
            name = '广州航空中心'
        #url = 'https://**********/querypush-web/a/qps/qpspacket/list'
        result = tool.getck(username, password, session, url,name)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        #pool = multiprocessing.Pool(processes=int(threads_combobox.get()))
        #pool.map(getmailtrace, datalist)
        # 并发执行并获取结果

        merged_data = {
            "总包号": [],
            "邮件号": []
        }
        #results = pool.map(getallmailtrace, datalist)
        #创建一个偏函数，其中root参数预先设定
        if organization_combobox.get()=='国际' and selected.get()!='实时':
            getzdmailtrace_bound = partial(getgjmailtrace, parent)
        elif organization_combobox.get()=='国内':
            getzdmailtrace_bound = partial(getgnmailtrace, parent)
        else:
            getzdmailtrace_bound = partial(getrealtime_mailtrace, parent)
        #getzdmailtrace_bound = partial(getmailtrace, parent)
        results = pool.map(getzdmailtrace_bound,datalist)
        # getmailtrace_bound = partial(getmailtrace, parent)
        # results =pool.map(getmailtrace_bound, datalist)
        merged_data = reduce(merge_dicts, results, merged_data)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()
        dataForm = pandas.DataFrame(merged_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel("总包内件明细"+"-"+
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)


        tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()

def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")




def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, selected2,submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,back_button,organization_combobox,input2_textbox

    # 构造Session
    session = requests.Session()
    today = datetime.datetime.today()
    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项



    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='开拆')
    radio_label = ttk.Label(input_label_container, text="总包类型:")
    radio_label.grid(row=0, column=0)
    radio_button1 = ttk.Radiobutton(input_label_container, text="开拆", value="开拆", variable=selected)
    radio_button1.grid(row=0, column=1)

    radio_button2 = ttk.Radiobutton(input_label_container, text="封发", value="封发", variable=selected)
    radio_button2.grid(row=0, column=2, padx=15)

    radio_button3 = ttk.Radiobutton(input_label_container, text="实时", value="实时", variable=selected)
    radio_button3.grid(row=0, column=3, padx=15)

    # 添加开始日期组件
    start_date_label = ttk.Label(input_label_container, text="开始日期:")
    start_date_label.grid(row=1, column=0, padx=10, pady=10)
    # start_date_label.pack()
    start_date_entry = DateEntry(input_label_container, maxdate=today.date())
    start_date_entry.grid(row=1, column=1, padx=10, pady=10)

    # 添加结束日期组件
    end_date_label = ttk.Label(input_label_container, text="结束日期:")
    end_date_label.grid(row=1, column=2, padx=10, pady=10)
    # end_date_label.pack()
    end_date_entry = DateEntry(input_label_container)
    end_date_entry.grid(row=1, column=3, padx=10, pady=10)
    # # 创建单选按钮
    # selected2 = tk.StringVar(value='否')
    # radio2_label = ttk.Label(input_label_container, text="是否只筛广航轨迹:")
    # radio2_label.grid(row=1, column=0, padx=10, pady=10)
    # radio2_button1 = ttk.Radiobutton(input_label_container, text="是", value='是', variable=selected2)
    # radio2_button1.grid(row=1, column=1, padx=5, pady=10)
    #
    # radio2_button2 = ttk.Radiobutton(input_label_container, text="否", value='否', variable=selected2)
    # radio2_button2.grid(row=1, column=2, padx=5, pady=10)
    #
    #
    # 添加指定处理动作
    # input2_label = ttk.Label(input_label_container, text="筛处理动作(多个处理动作用#分隔):")
    # input2_label.grid(row=2, column=0, padx=10, pady=10)
    # # input_label.pack()
    # input2_textbox = tk.Entry(input_label_container, width=30)
    # input2_textbox.grid(row=2, column=1, padx=10, pady=10, columnspan=1)
    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="总包号:")
    input_label.grid(row=3, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=3, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=4, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=4, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()