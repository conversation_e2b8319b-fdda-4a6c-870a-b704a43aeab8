import datetime
import io
import json
import os
import socket
import sys
import threading
import tkinter as tk
import traceback
import re

from datetime import timedelta
from tkinter import *
from tkinter import scrolledtext
from tkinter import ttk

from os import path


import pandas
import requests
import time

from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
from tool import Tool

# 以下为定义一个字典，以及需要获取的字段名称

dataOutput = {
    "关区代码": [],
    "关区名称": [],
    "机构或区域码": [],
    "机构或区域名称": []
}


def get_total_pages(response_text):
    """
    从响应文本中提取总页数
    """
    # 使用正则表达式匹配页数信息
    page_pattern = r'共\s*(\d+)页'
    match = re.search(page_pattern, response_text)
    if match:
        total_pages = int(match.group(1))
        print(f"检测到总页数: {total_pages}")
        return total_pages
    else:
        print("未能检测到页数信息，默认为1页")
        return 1


def fetch_data(page_no=1, page_size=20, org_level='04', query_district_code='', query_org_area_code=''):
    """
    获取关区信息数据
    """
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/basiccntr-web/a/basiccntr/districtorgrelation/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'pageNo': page_no,
        'pageSize': page_size,
        'orgLevel': org_level,
        'queryDistrictCode': query_district_code,
        'queryOrgAreaCode': query_org_area_code
    }
    print(f"请求第{page_no}页数据，参数: {data}")
    response = session.post(url, headers=headers, data=data, verify=False)
    return response.text


def parse_data(response_text, page_no):
    """
    解析HTML响应中的关区信息数据
    """
    if not response_text:
        print(f"第{page_no}页返回内容为空")
        return
    
    print(f"第{page_no}页返回内容长度: {len(response_text)}")
    
    html = BeautifulSoup(response_text, 'lxml')
    
    # 查找表格
    tbody = html.find('tbody')
    if not tbody:
        print(f"第{page_no}页未找到tbody")
        return
    
    trs = tbody.find_all('tr')
    print(f"第{page_no}页找到 {len(trs)} 行数据")
    
    for i, tr in enumerate(trs):
        tds = tr.find_all('td')
        if len(tds) >= 6:  # 确保有足够的列
            # 跳过第一列的复选框，提取后面4列的数据
            district_code = tds[1].get_text().strip()
            district_name = tds[2].get_text().strip()
            org_area_code = tds[3].get_text().strip()
            org_area_name = tds[4].get_text().strip()
            
            dataOutput["关区代码"].append(district_code)
            dataOutput["关区名称"].append(district_name)
            dataOutput["机构或区域码"].append(org_area_code)
            dataOutput["机构或区域名称"].append(org_area_name)
            
            print(f"第{page_no}页第{i+1}行: {district_code} | {district_name} | {org_area_code} | {org_area_name}")
        else:
            print(f"第{page_no}页第{i+1}行数据不足，只有 {len(tds)} 列")


def process_all_pages():
    """
    处理所有页面的数据
    """
    # 先获取第一页数据，确定总页数
    first_page_response = fetch_data(1)
    if not first_page_response:
        print("无法获取第一页数据")
        return
    
    # 解析第一页数据
    parse_data(first_page_response, 1)
    
    # 获取总页数
    total_pages = get_total_pages(first_page_response)
    
    if total_pages > 1:
        # 使用线程池并发获取剩余页面
        with ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
            futures = []
            for page_no in range(2, total_pages + 1):
                future = executor.submit(fetch_and_parse_page, page_no)
                futures.append(future)
            
            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"处理页面时出错: {e}")


def fetch_and_parse_page(page_no):
    """
    获取并解析指定页面的数据
    """
    response_text = fetch_data(page_no)
    if response_text:
        parse_data(response_text, page_no)
    else:
        print(f"无法获取第{page_no}页数据")


def run(title, parent):
    try:
        global username, password, session, jdptid, L, dataOutput
        L = 1
        
        # 清空数据
        for key in dataOutput.keys():
            dataOutput[key] = []
            
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")

        # 构造Session
        session = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")

        # 保存账号和密码
        tool.save_data()

        username = account_entry.get()
        password = password_entry.get()
        password = tool.aes_encrypt(password, 'B+oQ52IuAt9wbMxw')

        start = time.perf_counter()

        i = 2
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/basiccntr-web/a/basiccntr/districtorgrelation/list'
        result = tool.getck(username, password, session, url, '报关行')
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url, '报关行')
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)

        tool.process_input('开始获取关区信息数据')
        process_all_pages()

        tool.process_input('数据获取完毕')
        tool.process_input('正在写入Excel')
        
        # 统计数据量
        total_records = len(dataOutput["关区代码"])
        tool.process_input(f"共获取到 {total_records} 条关区信息")
        
        if total_records > 0:
            # 定义当前时间
            currentTime = datetime.datetime.now()
            
            dataForm = pandas.DataFrame(dataOutput)
            filename = "关区信息" + "-" + currentTime.strftime("%Y%m%d%H%M%S") + "-bylhx.xlsx"
            dataForm.to_excel(filename, index=False)
            tool.process_input(f"关区信息已导出到: {filename}")
        else:
            tool.process_input("没有获取到任何数据")

        tool.process_input("处理完成")

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")


def handle_input(title, parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    output_textbox.delete("1.0", "end")


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)


def open_func_window(parent, title):
    # 隐藏主窗口
    parent.withdraw()

    # 创建功能窗口
    func_window = tk.Toplevel(parent)
    func_window.title(title + " Power by LHX ")
    # 设置功能界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L, input_textbox, output_textbox, root, submit_button, button_clear, \
        account_entry, password_entry, threads_combobox, tool, back_button, organization_combobox, dataOutput

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行","国内","国际"])
    organization_combobox.set("国际")  # 默认选择第一个机构
    organization_combobox.grid(row=0, column=1, padx=5)

    # 添加账号输入框
    account_label = ttk.Label(account_container, text="账号:")
    account_label.grid(row=0, column=2, padx=10, pady=10)
    account_entry = Entry(account_container, width=20)
    account_entry.grid(row=0, column=3, padx=10, pady=10)

    # 添加密码输入框
    password_label = ttk.Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=10, pady=10)
    password_entry = Entry(account_container, show="*", width=20)
    password_entry.grid(row=0, column=5, padx=10, pady=10)

    # 添加线程数选择
    threads_label = ttk.Label(account_container, text="线程数:")
    threads_label.grid(row=0, column=6, padx=10, pady=10)
    threads_combobox = ttk.Combobox(account_container, values=["1", "2", "3", "4", "5"], width=5)
    threads_combobox.set("3")  # 默认3个线程
    threads_combobox.grid(row=0, column=7, padx=10, pady=10)

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 添加运行按钮
    submit_button = Button(input_label_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=0, column=0, padx=10, pady=10)

    # 添加清空按钮
    button_clear = Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=0, column=1, padx=10, pady=10)

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=2, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
