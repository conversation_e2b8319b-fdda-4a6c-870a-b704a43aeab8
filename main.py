import ctypes

import tkinter as tk

from main_menu import FunctionMenuApp


def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def show_admin_prompt():
    if not is_admin():
        tk.messagebox.showwarning("权限提示",
                                 "检测到当前程序没有管理员权限。\n"
                                 "某些功能可能无法正常工作。\n"
                                 "请右键点击程序图标，选择'以管理员身份运行'以获得全部功能。")
if __name__ == "__main__":
    root = tk.Tk()
    # 检查并提示管理员权限
    #root.withdraw()  # 隐藏主窗口
    show_admin_prompt()
    # 传递 current_version
    current_version = "1.6.10.10"
    app = FunctionMenuApp(root, current_version)


    root.mainloop()

